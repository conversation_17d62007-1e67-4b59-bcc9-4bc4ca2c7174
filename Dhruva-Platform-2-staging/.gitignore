# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.env
.venv
venv/
ENV/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.next/
out/
build/
.DS_Store
*.pem
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
.docker/
docker-compose.override.yml

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
*.log
logs/


# Database
*.sqlite3
*.db

# Temporary files
*.tmp
*.bak
*.swp
*~

# System Files
.DS_Store
Thumbs.db 